# 🚀 PLAN DE ACCIÓN ACTUALIZADO - DOCUMENSO

## 📊 Estado Actual del Proyecto

### ✅ **INFRAESTRUCTURA COMPLETADA:**
- **Servidor Proxmox:** Funcionando (prox.stargety.in)
- **Nginx Proxy Manager:** ✅ Configurado y operativo
- **Proxy para Documenso:** ✅ `sign.stargety.com` → `http://*************:3000`
- **Red:** ✅ Configurada (192.168.0.x/24, Gateway: ***********)
- **Scripts:** ✅ Desarrollados y listos para usar

### 🔄 **TAREAS RESTANTES:**
1. **Crear contenedor/VM en ***************
2. **Instalar Documenso en puerto 3000**
3. **Verificar funcionamiento completo**

---

## 🎯 PRÓXIMOS PASOS INMEDIATOS

### **Opción A: Acceso Manual al Servidor**

Si puedes acceder directamente al servidor Proxmox:

```bash
# 1. Conectar al servidor Proxmox
ssh <EMAIL>
# Contraseña: Netflix$1000

# 2. Crear contenedor LXC Ubuntu 22.04
pct create 102 local:vztmpl/ubuntu-22.04-standard_22.04-1_amd64.tar.zst \
  --hostname nuc2 \
  --memory 4096 \
  --cores 4 \
  --rootfs local-lvm:20 \
  --net0 name=eth0,bridge=vmbr0,ip=*************/24,gw=*********** \
  --nameserver ******* \
  --password

# 3. Iniciar el contenedor
pct start 102

# 4. Acceder al contenedor
pct enter 102
```

### **Opción B: Usar Interfaz Web de Proxmox**

1. **Acceder a Proxmox Web:**
   ```
   URL: https://prox.stargety.in:8006
   Usuario: root
   Contraseña: Netflix$1000
   ```

2. **Crear Contenedor LXC:**
   - Ir a: Datacenter > nuc1 > Create CT
   - Template: ubuntu-22.04-standard
   - Container ID: 102
   - Hostname: nuc2
   - Memory: 4096 MB
   - CPU: 4 cores
   - Root Disk: 20 GB
   - Network: 
     - IPv4: Static
     - IPv4/CIDR: *************/24
     - Gateway: ***********
     - DNS: *******

3. **Iniciar y acceder al contenedor**

---

## 📦 INSTALACIÓN DE DOCUMENSO

Una vez que el contenedor esté creado y funcionando en *************:

### **Paso 1: Preparar el Sistema**
```bash
# Actualizar sistema
apt update && apt upgrade -y

# Instalar dependencias básicas
apt install -y curl wget git unzip software-properties-common build-essential
```

### **Paso 2: Instalar Node.js 20**
```bash
# Instalar Node.js 20 LTS
curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
apt-get install -y nodejs

# Verificar instalación
node --version
npm --version
```

### **Paso 3: Instalar PostgreSQL**
```bash
# Instalar PostgreSQL
apt install -y postgresql postgresql-contrib

# Iniciar servicios
systemctl start postgresql
systemctl enable postgresql

# Configurar base de datos
sudo -u postgres createuser documenso
sudo -u postgres createdb documenso_db -O documenso
sudo -u postgres psql -c "ALTER USER documenso PASSWORD 'DocumensoPass2025!';"
```

### **Paso 4: Instalar Documenso**
```bash
# Crear directorio
mkdir -p /opt/documenso
cd /opt/documenso

# Descargar última versión
LATEST_VERSION=$(curl -s https://api.github.com/repos/documenso/documenso/releases/latest | grep tag_name | cut -d '"' -f 4)
wget https://github.com/documenso/documenso/archive/refs/tags/${LATEST_VERSION}.zip
unzip ${LATEST_VERSION}.zip
mv documenso-${LATEST_VERSION#v}/* .
rm -rf documenso-${LATEST_VERSION#v} ${LATEST_VERSION}.zip

# Configurar entorno
cp .env.example .env
```

### **Paso 5: Configurar Variables de Entorno**
```bash
# Editar .env
nano .env
```

**Contenido del archivo .env:**
```env
# Database
DATABASE_URL="postgresql://documenso:DocumensoPass2025!@localhost:5432/documenso_db"

# Application
NEXTAUTH_URL="http://*************:3000"
NEXTAUTH_SECRET="$(openssl rand -base64 32)"

# Public URLs
NEXT_PUBLIC_WEBAPP_URL="http://*************:3000"

# Encryption
NEXT_PRIVATE_ENCRYPTION_KEY="$(openssl rand -base64 32)"
NEXT_PRIVATE_ENCRYPTION_SECONDARY_KEY="$(openssl rand -base64 32)"

# Environment
NODE_ENV="production"
```

### **Paso 6: Construir e Instalar**
```bash
# Instalar dependencias
npm install

# Construir aplicación
npm run build

# Ejecutar migraciones
npm run prisma:migrate:deploy
npm run prisma:generate
```

### **Paso 7: Crear Servicio Systemd**
```bash
# Crear archivo de servicio
cat > /etc/systemd/system/documenso.service << 'EOF'
[Unit]
Description=Documenso Application
After=network.target postgresql.service

[Service]
Type=simple
User=root
WorkingDirectory=/opt/documenso
Environment=NODE_ENV=production
ExecStart=/usr/bin/npm start
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Habilitar y iniciar servicio
systemctl daemon-reload
systemctl enable documenso
systemctl start documenso
```

---

## 🔍 VERIFICACIÓN

### **Verificar Servicios**
```bash
# Estado de Documenso
systemctl status documenso

# Estado de PostgreSQL
systemctl status postgresql

# Verificar puerto 3000
netstat -tlnp | grep :3000

# Logs de Documenso
journalctl -u documenso -f
```

### **Verificar Conectividad**
```bash
# Desde el contenedor
curl -I http://localhost:3000

# Desde el servidor Proxmox
curl -I http://*************:3000
```

### **Verificar Proxy**
- Abrir navegador: `http://sign.stargety.com`
- Debería redirigir a Documenso

---

## 🌐 URLS DE ACCESO FINAL

| Tipo | URL | Estado |
|------|-----|--------|
| **Directo** | http://*************:3000 | ✅ Listo |
| **Dominio** | http://sign.stargety.com | ✅ Configurado |
| **HTTPS** | https://sign.stargety.com | ⏳ Pendiente SSL |

---

## 🔧 SOLUCIÓN DE PROBLEMAS

### **Si Documenso no inicia:**
```bash
# Verificar logs
journalctl -u documenso -n 50

# Verificar configuración
cd /opt/documenso
npm run dev  # Para debugging
```

### **Si la base de datos no conecta:**
```bash
# Verificar PostgreSQL
systemctl status postgresql
sudo -u postgres psql -l

# Verificar usuario y permisos
sudo -u postgres psql -c "\du"
```

### **Si el proxy no funciona:**
- Verificar que Nginx Proxy Manager esté funcionando
- Verificar que *************:3000 responda
- Revisar logs de Nginx Proxy Manager

---

## 📞 COMANDOS ÚTILES

```bash
# Gestión del contenedor (desde Proxmox)
pct list                    # Listar contenedores
pct status 102             # Estado del contenedor
pct start 102              # Iniciar contenedor
pct stop 102               # Detener contenedor
pct enter 102              # Acceder al contenedor

# Gestión de Documenso (desde el contenedor)
systemctl status documenso    # Estado del servicio
systemctl restart documenso   # Reiniciar servicio
journalctl -u documenso -f    # Ver logs en tiempo real
```

---

**🎉 Una vez completados estos pasos, Documenso estará funcionando en `http://sign.stargety.com`**
