# 🚀 DOCUMENSO - GUÍA DE INICIO RÁPIDO

## ⚡ Instalación en 3 Pasos

### 📋 Prerrequisitos
- ✅ VM nuc2 creada y ejecutándose en Proxmox
- ✅ Ubuntu Server 24.04.2 instalado
- ✅ Scripts de instalación descargados

---

## 🎯 PASO 1: Completar Ubuntu Server

1. **Acceder a la consola de la VM:**
   ```
   URL: https://prox.stargety.in:8006
   Usuario: root / Contraseña: Netflix$1000
   ```

2. **Navegar a:** Datacenter > nuc1 > Virtual Machines > 102 (nuc2) > Console

3. **Configurar durante la instalación:**
   - Usuario: `documenso`
   - Contraseña: `Documenso2025!`
   - Hostname: `nuc2`
   - ✅ Instalar OpenSSH Server

---

## 🎯 PASO 2: Transferir Scripts

```bash
# Desde tu máquina local
scp configure-static-ip.sh install-documenso.sh setup-documenso-complete.sh documenso@[IP_TEMPORAL]:~/
```

---

## 🎯 PASO 3: Ejecutar Instalación Automatizada

```bash
# Conectar a la VM
ssh documenso@[IP_TEMPORAL]

# Ejecutar script maestro
chmod +x setup-documenso-complete.sh
./setup-documenso-complete.sh
```

**¡Eso es todo!** El script se encarga de:
- ✅ Configurar IP estática (*************)
- ✅ Instalar Node.js, PostgreSQL, Nginx
- ✅ Descargar e instalar Documenso
- ✅ Configurar base de datos
- ✅ Configurar servicios systemd
- ✅ Configurar firewall
- ✅ Configurar proxy Nginx

---

## 🌐 Acceso Post-instalación

| URL | Descripción |
|-----|-------------|
| http://*************:3000 | Acceso directo |
| http://************* | A través de Nginx |
| http://sign.stargety.com | Con dominio (requiere DNS) |

---

## 🔧 Comandos Útiles

```bash
# Ver estado
sudo systemctl status documenso

# Ver logs
sudo journalctl -u documenso -f

# Reiniciar
sudo systemctl restart documenso

# Verificar red
/usr/local/bin/verify-network.sh
```

---

## 🆘 Si Algo Sale Mal

1. **Revisar logs:** `sudo journalctl -u documenso -n 50`
2. **Verificar servicios:** `sudo systemctl status documenso nginx postgresql`
3. **Verificar red:** `/usr/local/bin/verify-network.sh`
4. **Ejecutar scripts individuales:**
   ```bash
   sudo ./configure-static-ip.sh
   ./install-documenso.sh
   ```

---

## 📞 Credenciales Importantes

| Sistema | Usuario | Contraseña |
|---------|---------|------------|
| VM SSH | documenso | Documenso2025! |
| PostgreSQL | documenso | DocumensoPass2025! |
| Proxmox | root | Netflix$1000 |

---

**🎉 ¡Disfruta tu nueva plataforma de firma de documentos!**

*Para más detalles, consulta: `documenso-installation-guide.md`*
