# Instalación de Documenso en Proxmox

## Estado Actual del Proyecto

✅ **VM nuc2 creada exitosamente**
- **VM ID:** 102
- **Nombre:** nuc2
- **Estado:** Running
- **Especificaciones:**
  - CPU: 4 cores
  - RAM: 4096 MB
  - Almacenamiento: 20 GB (local-lvm)
  - Red: virtio,bridge=vmbr0
  - ISO: Ubuntu 24.04.2 LTS Server

---

# Conexión SSH al Servidor Proxmox

## Información del Servidor

- **Servidor:** prox.stargety.in
- **Usuario:** root
- **Contraseña:** Netflix$1000
- **Puerto SSH:** 22 (puerto estándar)
- **Sistema:** Debian GNU/Linux con Proxmox VE
- **Kernel:** 6.8.12-9-pve
- **Hostname:** nuc1

## Instrucciones de Conexión

### Método 1: Conexión SSH Básica

```bash
ssh <EMAIL>
```

Cuando se solicite la contraseña, ingresa: `Netflix$1000`

### Método 2: Conexión SSH con sshpass (Automatizada)

Si tienes `sshpass` instalado, puedes usar:

```bash
sshpass -p 'Netflix$1000' ssh -o StrictHostKeyChecking=no <EMAIL>
```

#### Instalar sshpass (si no está disponible):

**En Ubuntu/Debian:**
```bash
sudo apt install sshpass -y
```

**En CentOS/RHEL:**
```bash
sudo yum install sshpass -y
```

**En macOS:**
```bash
brew install sshpass
```

### Método 3: Configuración de SSH Key (Recomendado para uso frecuente)

Para mayor seguridad y comodidad, configura autenticación por clave SSH:

1. **Generar clave SSH (si no tienes una):**
```bash
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
```

2. **Copiar clave pública al servidor:**
```bash
ssh-copy-id <EMAIL>
```

3. **Conectar sin contraseña:**
```bash
ssh <EMAIL>
```

## Verificación de Conexión

Una vez conectado, deberías ver algo similar a:

```
Linux nuc1 6.8.12-9-pve #1 SMP PREEMPT_DYNAMIC PMX 6.8.12-9 (2025-03-16T19:18Z) x86_64

The programs included with the Debian GNU/Linux system are free software;
the exact distribution terms for each program are described in the
individual files in /usr/share/doc/*/copyright.

Debian GNU/Linux comes with ABSOLUTELY NO WARRANTY, to the extent
permitted by applicable law.
Last login: Thu Jul 10 19:02:47 2025
root@nuc1:~# 
```

## Comandos Útiles de Proxmox

### Información del Sistema
```bash
# Ver versión de Proxmox
pveversion

# Estado del cluster
pvecm status

# Información de nodos
pvecm nodes

# Uso de recursos
pvesh get /nodes/nuc1/status
```

### Gestión de VMs
```bash
# Listar todas las VMs
qm list

# Estado de una VM específica
qm status <vmid>

# Iniciar una VM
qm start <vmid>

# Detener una VM
qm stop <vmid>
```

### Gestión de Contenedores LXC
```bash
# Listar contenedores
pct list

# Estado de un contenedor
pct status <ctid>

# Iniciar contenedor
pct start <ctid>

# Detener contenedor
pct stop <ctid>
```

### Gestión de Almacenamiento
```bash
# Ver almacenamiento disponible
pvesm status

# Información detallada de almacenamiento
pvesh get /storage
```

## Acceso Web a Proxmox

También puedes acceder a la interfaz web de Proxmox:

- **URL:** https://prox.stargety.in:8006
- **Usuario:** root
- **Contraseña:** Netflix$1000
- **Realm:** Linux PAM standard authentication

## Notas de Seguridad

⚠️ **Importante:**
- Cambia la contraseña por defecto lo antes posible
- Configura autenticación por clave SSH
- Considera crear usuarios adicionales con permisos limitados
- Habilita firewall y configura reglas apropiadas
- Mantén el sistema actualizado regularmente

## Solución de Problemas

### Error de Conexión
Si no puedes conectarte:
1. Verifica que el puerto 22 esté abierto
2. Confirma que el servicio SSH esté ejecutándose
3. Revisa las reglas del firewall

### Error de Autenticación
Si la autenticación falla:
1. Verifica que la contraseña sea correcta
2. Asegúrate de usar el usuario correcto (root)
3. Revisa los logs de SSH: `tail -f /var/log/auth.log`

### Primera Conexión
En la primera conexión, se te pedirá confirmar la huella digital del servidor:
```
Are you sure you want to continue connecting (yes/no/[fingerprint])? yes
```

Responde `yes` para continuar.

## Comandos de Desconexión

Para salir de la sesión SSH:
```bash
exit
```

O usa la combinación de teclas: `Ctrl + D`
