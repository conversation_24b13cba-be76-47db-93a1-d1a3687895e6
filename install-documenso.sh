#!/bin/bash

# Documenso Installation Script for Ubuntu Server
# Target: VM nuc2 (*************) on Proxmox
# Author: Augment Agent
# Date: 2025-07-10

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration variables
DB_NAME="documenso_db"
DB_USER="documenso"
DB_PASS="DocumensoPass2025!"
APP_USER="documenso"
APP_DIR="/opt/documenso"
DOMAIN="sign.stargety.com"
VM_IP="*************"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_error "This script should not be run as root. Please run as a regular user with sudo privileges."
        exit 1
    fi
}

# Function to update system
update_system() {
    print_status "Updating system packages..."
    sudo apt update && sudo apt upgrade -y
    print_success "System updated successfully"
}

# Function to install basic dependencies
install_dependencies() {
    print_status "Installing basic dependencies..."
    sudo apt install -y curl wget git unzip software-properties-common build-essential
    print_success "Basic dependencies installed"
}

# Function to install Node.js
install_nodejs() {
    print_status "Installing Node.js 20 LTS..."
    
    if command_exists node; then
        NODE_VERSION=$(node --version)
        print_warning "Node.js is already installed: $NODE_VERSION"
        return 0
    fi
    
    curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
    sudo apt-get install -y nodejs
    
    # Verify installation
    if command_exists node && command_exists npm; then
        print_success "Node.js $(node --version) and npm $(npm --version) installed successfully"
    else
        print_error "Failed to install Node.js"
        exit 1
    fi
}

# Function to install PostgreSQL
install_postgresql() {
    print_status "Installing PostgreSQL..."
    
    sudo apt install -y postgresql postgresql-contrib
    sudo systemctl start postgresql
    sudo systemctl enable postgresql
    
    print_success "PostgreSQL installed and started"
}

# Function to configure PostgreSQL
configure_postgresql() {
    print_status "Configuring PostgreSQL database..."
    
    # Create database user and database
    sudo -u postgres psql -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASS';" 2>/dev/null || true
    sudo -u postgres psql -c "CREATE DATABASE $DB_NAME OWNER $DB_USER;" 2>/dev/null || true
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;" 2>/dev/null || true
    
    print_success "PostgreSQL configured successfully"
}

# Function to create application user
create_app_user() {
    print_status "Creating application user..."
    
    if id "$APP_USER" &>/dev/null; then
        print_warning "User $APP_USER already exists"
    else
        sudo useradd -m -s /bin/bash $APP_USER
        print_success "User $APP_USER created"
    fi
}

# Function to download and setup Documenso
setup_documenso() {
    print_status "Setting up Documenso application..."
    
    # Create application directory
    sudo mkdir -p $APP_DIR
    cd $APP_DIR
    
    # Get latest version
    print_status "Fetching latest Documenso version..."
    LATEST_VERSION=$(curl -s https://api.github.com/repos/documenso/documenso/releases/latest | grep tag_name | cut -d '"' -f 4)
    print_status "Latest version: $LATEST_VERSION"
    
    # Download and extract
    wget -q https://github.com/documenso/documenso/archive/refs/tags/${LATEST_VERSION}.zip
    unzip -q ${LATEST_VERSION}.zip
    sudo mv documenso-${LATEST_VERSION#v}/* .
    sudo rm -rf documenso-${LATEST_VERSION#v} ${LATEST_VERSION}.zip
    
    # Set ownership
    sudo chown -R $APP_USER:$APP_USER $APP_DIR
    
    print_success "Documenso source code downloaded and extracted"
}

# Function to configure Documenso environment
configure_documenso() {
    print_status "Configuring Documenso environment..."
    
    cd $APP_DIR
    
    # Create .env file
    sudo -u $APP_USER cp .env.example .env
    
    # Generate secrets
    NEXTAUTH_SECRET=$(openssl rand -base64 32)
    ENCRYPTION_KEY=$(openssl rand -base64 32)
    SECONDARY_KEY=$(openssl rand -base64 32)
    
    # Configure .env file
    sudo -u $APP_USER tee .env > /dev/null <<EOF
# Database
DATABASE_URL="postgresql://$DB_USER:$DB_PASS@localhost:5432/$DB_NAME"

# Application
NEXTAUTH_URL="http://$VM_IP:3000"
NEXTAUTH_SECRET="$NEXTAUTH_SECRET"

# Public URLs
NEXT_PUBLIC_WEBAPP_URL="http://$VM_IP:3000"

# Encryption
NEXT_PRIVATE_ENCRYPTION_KEY="$ENCRYPTION_KEY"
NEXT_PRIVATE_ENCRYPTION_SECONDARY_KEY="$SECONDARY_KEY"

# Email (configure later)
SMTP_HOST=""
SMTP_PORT=""
SMTP_USERNAME=""
SMTP_PASSWORD=""
SMTP_FROM="noreply@$DOMAIN"

# Feature flags
NEXT_PUBLIC_ALLOW_SIGNUP="true"
NEXT_PUBLIC_DISABLE_SIGNUP="false"

# Environment
NODE_ENV="production"
EOF
    
    print_success "Environment configuration created"
}

# Function to install npm dependencies and build
build_documenso() {
    print_status "Installing dependencies and building Documenso..."
    
    cd $APP_DIR
    
    # Install dependencies as app user
    sudo -u $APP_USER npm install
    
    # Build the application
    sudo -u $APP_USER npm run build
    
    # Run database migrations
    sudo -u $APP_USER npm run prisma:migrate:deploy
    
    # Generate Prisma client
    sudo -u $APP_USER npm run prisma:generate
    
    print_success "Documenso built successfully"
}

# Function to create systemd service
create_systemd_service() {
    print_status "Creating systemd service..."
    
    sudo tee /etc/systemd/system/documenso.service > /dev/null <<EOF
[Unit]
Description=Documenso Application
After=network.target postgresql.service

[Service]
Type=simple
User=$APP_USER
WorkingDirectory=$APP_DIR
Environment=NODE_ENV=production
ExecStart=/usr/bin/npm start
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    # Reload systemd and enable service
    sudo systemctl daemon-reload
    sudo systemctl enable documenso
    
    print_success "Systemd service created and enabled"
}

# Function to install and configure Nginx
install_nginx() {
    print_status "Installing and configuring Nginx..."
    
    sudo apt install -y nginx
    
    # Create Nginx configuration
    sudo tee /etc/nginx/sites-available/$DOMAIN > /dev/null <<EOF
server {
    listen 80;
    server_name $DOMAIN $VM_IP;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 86400;
    }
}
EOF
    
    # Enable site
    sudo ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # Test and reload Nginx
    sudo nginx -t
    sudo systemctl enable nginx
    sudo systemctl reload nginx
    
    print_success "Nginx installed and configured"
}

# Function to configure firewall
configure_firewall() {
    print_status "Configuring firewall..."
    
    sudo ufw --force enable
    sudo ufw allow ssh
    sudo ufw allow 80
    sudo ufw allow 443
    sudo ufw allow 3000
    
    print_success "Firewall configured"
}

# Function to start services
start_services() {
    print_status "Starting services..."
    
    sudo systemctl start documenso
    sudo systemctl start nginx
    
    # Wait a moment for services to start
    sleep 5
    
    # Check service status
    if sudo systemctl is-active --quiet documenso; then
        print_success "Documenso service is running"
    else
        print_error "Documenso service failed to start"
        sudo journalctl -u documenso --no-pager -n 20
    fi
    
    if sudo systemctl is-active --quiet nginx; then
        print_success "Nginx service is running"
    else
        print_error "Nginx service failed to start"
    fi
}

# Function to display final information
display_info() {
    print_success "Documenso installation completed!"
    echo
    echo "=== ACCESS INFORMATION ==="
    echo "Local access: http://localhost:3000"
    echo "Network access: http://$VM_IP:3000"
    echo "Domain access: http://$DOMAIN"
    echo "With Nginx: http://$VM_IP"
    echo
    echo "=== CREDENTIALS ==="
    echo "Database: $DB_NAME"
    echo "DB User: $DB_USER"
    echo "DB Password: $DB_PASS"
    echo
    echo "=== USEFUL COMMANDS ==="
    echo "Check Documenso status: sudo systemctl status documenso"
    echo "View Documenso logs: sudo journalctl -u documenso -f"
    echo "Restart Documenso: sudo systemctl restart documenso"
    echo "Check Nginx status: sudo systemctl status nginx"
    echo
    echo "=== NEXT STEPS ==="
    echo "1. Configure DNS to point $DOMAIN to $VM_IP"
    echo "2. Set up SSL certificate with: sudo certbot --nginx -d $DOMAIN"
    echo "3. Configure email settings in $APP_DIR/.env"
    echo "4. Create your first admin user at: http://$VM_IP:3000"
}

# Main installation function
main() {
    print_status "Starting Documenso installation..."
    
    check_root
    update_system
    install_dependencies
    install_nodejs
    install_postgresql
    configure_postgresql
    create_app_user
    setup_documenso
    configure_documenso
    build_documenso
    create_systemd_service
    install_nginx
    configure_firewall
    start_services
    display_info
    
    print_success "Installation completed successfully!"
}

# Run main function
main "$@"
