#!/bin/bash

# Complete Documenso Setup Script for Proxmox VM nuc2
# This script orchestrates the complete installation process
# Author: Augment Agent
# Date: 2025-07-10

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
VM_IP="*************"
DOMAIN="sign.stargety.com"
PROXMOX_HOST="prox.stargety.in"

# Function to print colored output
print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# Function to display banner
display_banner() {
    clear
    echo -e "${PURPLE}"
    cat << "EOF"
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║               DOCUMENSO INSTALLATION SUITE                  ║
║                                                              ║
║  Complete setup for Documenso on Proxmox VM nuc2           ║
║  Target: ************* (sign.stargety.com)                 ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# Function to check prerequisites
check_prerequisites() {
    print_header "CHECKING PREREQUISITES"
    
    # Check if running on the correct system
    if [[ ! -f /etc/os-release ]]; then
        print_error "Cannot determine operating system"
        exit 1
    fi
    
    # Check if scripts exist
    local scripts=("configure-static-ip.sh" "install-documenso.sh")
    for script in "${scripts[@]}"; do
        if [[ ! -f "$SCRIPT_DIR/$script" ]]; then
            print_error "Required script not found: $script"
            exit 1
        fi
        
        if [[ ! -x "$SCRIPT_DIR/$script" ]]; then
            print_status "Making $script executable..."
            chmod +x "$SCRIPT_DIR/$script"
        fi
    done
    
    print_success "Prerequisites check completed"
}

# Function to display current system status
display_system_status() {
    print_header "CURRENT SYSTEM STATUS"
    
    echo "System Information:"
    echo "  OS: $(grep PRETTY_NAME /etc/os-release | cut -d'"' -f2)"
    echo "  Hostname: $(hostname)"
    echo "  Kernel: $(uname -r)"
    echo
    
    echo "Network Configuration:"
    echo "  Current IP: $(ip route get ******* | grep -oP 'src \K\S+')"
    echo "  Target IP: $VM_IP"
    echo "  Gateway: $(ip route | grep default | awk '{print $3}')"
    echo
    
    echo "System Resources:"
    echo "  CPU Cores: $(nproc)"
    echo "  Memory: $(free -h | grep Mem | awk '{print $2}')"
    echo "  Disk Space: $(df -h / | tail -1 | awk '{print $4}') available"
    echo
}

# Function to configure static IP
configure_network() {
    print_header "CONFIGURING STATIC IP ADDRESS"
    
    current_ip=$(ip route get ******* | grep -oP 'src \K\S+')
    
    if [[ "$current_ip" == "$VM_IP" ]]; then
        print_success "Static IP $VM_IP is already configured"
        return 0
    fi
    
    print_status "Current IP: $current_ip"
    print_status "Target IP: $VM_IP"
    print_warning "This will change your network configuration and may disconnect SSH"
    
    read -p "Do you want to continue with network configuration? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_warning "Network configuration skipped"
        return 1
    fi
    
    print_status "Running network configuration script..."
    sudo "$SCRIPT_DIR/configure-static-ip.sh"
    
    print_success "Network configuration completed"
    print_warning "Please verify network connectivity before proceeding"
    
    # Wait for network to stabilize
    sleep 5
    
    # Test connectivity
    if ping -c 3 ******* >/dev/null 2>&1; then
        print_success "Network connectivity verified"
    else
        print_error "Network connectivity test failed"
        return 1
    fi
}

# Function to install Documenso
install_documenso() {
    print_header "INSTALLING DOCUMENSO"
    
    print_status "Running Documenso installation script..."
    "$SCRIPT_DIR/install-documenso.sh"
    
    print_success "Documenso installation completed"
}

# Function to configure SSL (optional)
configure_ssl() {
    print_header "SSL CONFIGURATION (OPTIONAL)"
    
    print_status "SSL configuration requires:"
    echo "  1. Domain $DOMAIN must point to this server"
    echo "  2. Ports 80 and 443 must be accessible from the internet"
    echo "  3. Valid email address for Let's Encrypt"
    echo
    
    read -p "Do you want to configure SSL now? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_warning "SSL configuration skipped"
        print_status "You can configure SSL later with: sudo certbot --nginx -d $DOMAIN"
        return 0
    fi
    
    # Check if certbot is installed
    if ! command -v certbot >/dev/null 2>&1; then
        print_status "Installing Certbot..."
        sudo apt update
        sudo apt install -y certbot python3-certbot-nginx
    fi
    
    # Get email for Let's Encrypt
    read -p "Enter email address for Let's Encrypt: " email
    
    if [[ -z "$email" ]]; then
        print_error "Email address is required for SSL certificate"
        return 1
    fi
    
    print_status "Obtaining SSL certificate for $DOMAIN..."
    sudo certbot --nginx -d "$DOMAIN" --email "$email" --agree-tos --non-interactive
    
    if [[ $? -eq 0 ]]; then
        print_success "SSL certificate configured successfully"
    else
        print_error "SSL certificate configuration failed"
        return 1
    fi
}

# Function to perform final verification
final_verification() {
    print_header "FINAL VERIFICATION"
    
    print_status "Checking service status..."
    
    # Check Documenso service
    if sudo systemctl is-active --quiet documenso; then
        print_success "Documenso service is running"
    else
        print_error "Documenso service is not running"
        sudo systemctl status documenso --no-pager -l
    fi
    
    # Check Nginx service
    if sudo systemctl is-active --quiet nginx; then
        print_success "Nginx service is running"
    else
        print_error "Nginx service is not running"
        sudo systemctl status nginx --no-pager -l
    fi
    
    # Check PostgreSQL service
    if sudo systemctl is-active --quiet postgresql; then
        print_success "PostgreSQL service is running"
    else
        print_error "PostgreSQL service is not running"
        sudo systemctl status postgresql --no-pager -l
    fi
    
    print_status "Testing HTTP connectivity..."
    
    # Test local connectivity
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 | grep -q "200\|302"; then
        print_success "Local HTTP (port 3000) is responding"
    else
        print_warning "Local HTTP (port 3000) test failed"
    fi
    
    # Test Nginx proxy
    if curl -s -o /dev/null -w "%{http_code}" http://localhost | grep -q "200\|302"; then
        print_success "Nginx proxy is responding"
    else
        print_warning "Nginx proxy test failed"
    fi
}

# Function to display final information
display_final_info() {
    print_header "INSTALLATION COMPLETED"
    
    echo -e "${GREEN}Documenso has been successfully installed!${NC}"
    echo
    echo "=== ACCESS INFORMATION ==="
    echo "  Direct access: http://$VM_IP:3000"
    echo "  Nginx proxy: http://$VM_IP"
    echo "  Domain access: http://$DOMAIN"
    
    if sudo nginx -T 2>/dev/null | grep -q "ssl_certificate"; then
        echo "  HTTPS access: https://$DOMAIN"
    fi
    
    echo
    echo "=== SYSTEM CREDENTIALS ==="
    echo "  SSH: documenso@$VM_IP"
    echo "  Password: Documenso2025!"
    echo
    echo "=== DATABASE CREDENTIALS ==="
    echo "  Database: documenso_db"
    echo "  User: documenso"
    echo "  Password: DocumensoPass2025!"
    echo
    echo "=== USEFUL COMMANDS ==="
    echo "  Check status: sudo systemctl status documenso"
    echo "  View logs: sudo journalctl -u documenso -f"
    echo "  Restart service: sudo systemctl restart documenso"
    echo "  Verify network: /usr/local/bin/verify-network.sh"
    echo
    echo "=== NEXT STEPS ==="
    echo "  1. Open http://$VM_IP:3000 in your browser"
    echo "  2. Create your first admin user"
    echo "  3. Configure email settings in /opt/documenso/.env"
    echo "  4. Set up DNS for $DOMAIN if not already done"
    
    if ! sudo nginx -T 2>/dev/null | grep -q "ssl_certificate"; then
        echo "  5. Configure SSL: sudo certbot --nginx -d $DOMAIN"
    fi
    
    echo
    print_success "Setup completed successfully!"
}

# Function to handle errors
handle_error() {
    print_error "An error occurred during installation"
    echo "Check the logs above for details"
    echo "You can run individual scripts manually:"
    echo "  - Network: sudo $SCRIPT_DIR/configure-static-ip.sh"
    echo "  - Documenso: $SCRIPT_DIR/install-documenso.sh"
    exit 1
}

# Main installation function
main() {
    # Set error handler
    trap handle_error ERR
    
    display_banner
    
    print_status "Starting complete Documenso setup..."
    echo "This script will:"
    echo "  1. Configure static IP address ($VM_IP)"
    echo "  2. Install and configure Documenso"
    echo "  3. Set up Nginx reverse proxy"
    echo "  4. Configure firewall"
    echo "  5. Optionally set up SSL certificate"
    echo
    
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_warning "Installation cancelled by user"
        exit 0
    fi
    
    check_prerequisites
    display_system_status
    
    # Configure network (may require manual intervention)
    if configure_network; then
        print_success "Network configuration successful"
    else
        print_warning "Network configuration was skipped or failed"
        print_status "Continuing with current network configuration..."
    fi
    
    # Install Documenso
    install_documenso
    
    # Configure SSL (optional)
    configure_ssl
    
    # Final verification
    final_verification
    
    # Display final information
    display_final_info
}

# Check if script is being run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
