#!/bin/bash

# Static IP Configuration Script for Ubuntu Server
# Target: VM nuc2 (*************) on Proxmox
# Author: Augment Agent
# Date: 2025-07-10

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Network configuration
TARGET_IP="*************"
NETMASK="*************"
GATEWAY="***********"
DNS1="*******"
DNS2="*******"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root. Use: sudo $0"
        exit 1
    fi
}

# Function to backup current network configuration
backup_network_config() {
    print_status "Backing up current network configuration..."
    
    if [ -f /etc/netplan/00-installer-config.yaml ]; then
        cp /etc/netplan/00-installer-config.yaml /etc/netplan/00-installer-config.yaml.backup
        print_success "Backup created: /etc/netplan/00-installer-config.yaml.backup"
    fi
    
    if [ -f /etc/netplan/50-cloud-init.yaml ]; then
        cp /etc/netplan/50-cloud-init.yaml /etc/netplan/50-cloud-init.yaml.backup
        print_success "Backup created: /etc/netplan/50-cloud-init.yaml.backup"
    fi
}

# Function to detect network interface
detect_interface() {
    print_status "Detecting network interface..."
    
    # Get the main network interface (excluding loopback)
    INTERFACE=$(ip route | grep default | awk '{print $5}' | head -n1)
    
    if [ -z "$INTERFACE" ]; then
        # Fallback: get first non-loopback interface
        INTERFACE=$(ip link show | grep -E "^[0-9]+:" | grep -v lo | head -n1 | awk -F': ' '{print $2}')
    fi
    
    if [ -z "$INTERFACE" ]; then
        print_error "Could not detect network interface"
        exit 1
    fi
    
    print_success "Detected network interface: $INTERFACE"
}

# Function to create netplan configuration
create_netplan_config() {
    print_status "Creating netplan configuration..."
    
    # Remove existing configurations
    rm -f /etc/netplan/00-installer-config.yaml
    rm -f /etc/netplan/50-cloud-init.yaml
    
    # Create new static IP configuration
    cat > /etc/netplan/01-static-config.yaml <<EOF
network:
  version: 2
  renderer: networkd
  ethernets:
    $INTERFACE:
      dhcp4: false
      dhcp6: false
      addresses:
        - $TARGET_IP/24
      gateway4: $GATEWAY
      nameservers:
        addresses:
          - $DNS1
          - $DNS2
        search:
          - localdomain
EOF
    
    print_success "Netplan configuration created"
}

# Function to apply network configuration
apply_network_config() {
    print_status "Applying network configuration..."
    
    # Test the configuration
    if netplan try --timeout=30; then
        print_success "Network configuration applied successfully"
    else
        print_error "Failed to apply network configuration"
        print_warning "Restoring backup configuration..."
        
        # Restore backup if it exists
        if [ -f /etc/netplan/00-installer-config.yaml.backup ]; then
            mv /etc/netplan/00-installer-config.yaml.backup /etc/netplan/00-installer-config.yaml
            rm -f /etc/netplan/01-static-config.yaml
            netplan apply
            print_warning "Backup configuration restored"
        fi
        
        exit 1
    fi
}

# Function to verify network connectivity
verify_connectivity() {
    print_status "Verifying network connectivity..."
    
    # Check if IP is assigned
    if ip addr show $INTERFACE | grep -q "$TARGET_IP"; then
        print_success "IP address $TARGET_IP assigned to $INTERFACE"
    else
        print_error "IP address not assigned correctly"
        return 1
    fi
    
    # Check gateway connectivity
    if ping -c 3 $GATEWAY >/dev/null 2>&1; then
        print_success "Gateway $GATEWAY is reachable"
    else
        print_warning "Gateway $GATEWAY is not reachable"
    fi
    
    # Check DNS connectivity
    if ping -c 3 $DNS1 >/dev/null 2>&1; then
        print_success "DNS server $DNS1 is reachable"
    else
        print_warning "DNS server $DNS1 is not reachable"
    fi
    
    # Check internet connectivity
    if ping -c 3 google.com >/dev/null 2>&1; then
        print_success "Internet connectivity verified"
    else
        print_warning "Internet connectivity test failed"
    fi
}

# Function to update /etc/hosts
update_hosts_file() {
    print_status "Updating /etc/hosts file..."
    
    # Remove existing entries for the hostname
    sed -i '/nuc2/d' /etc/hosts
    
    # Add new entry
    echo "$TARGET_IP nuc2 nuc2.localdomain" >> /etc/hosts
    
    print_success "/etc/hosts updated"
}

# Function to display network information
display_network_info() {
    print_success "Network configuration completed!"
    echo
    echo "=== NETWORK CONFIGURATION ==="
    echo "Interface: $INTERFACE"
    echo "IP Address: $TARGET_IP/24"
    echo "Gateway: $GATEWAY"
    echo "DNS Servers: $DNS1, $DNS2"
    echo
    echo "=== CURRENT NETWORK STATUS ==="
    ip addr show $INTERFACE
    echo
    echo "=== ROUTING TABLE ==="
    ip route
    echo
    echo "=== DNS CONFIGURATION ==="
    cat /etc/resolv.conf
    echo
    echo "=== CONNECTIVITY TESTS ==="
    echo "Gateway ping test:"
    ping -c 3 $GATEWAY || echo "Gateway unreachable"
    echo
    echo "DNS ping test:"
    ping -c 3 $DNS1 || echo "DNS unreachable"
    echo
    echo "Internet connectivity test:"
    ping -c 3 google.com || echo "Internet unreachable"
}

# Function to create network verification script
create_verification_script() {
    print_status "Creating network verification script..."
    
    cat > /usr/local/bin/verify-network.sh <<'EOF'
#!/bin/bash

# Network verification script
echo "=== Network Status Verification ==="
echo "Date: $(date)"
echo

echo "Network Interfaces:"
ip addr show | grep -E "(inet |UP|DOWN)"
echo

echo "Routing Table:"
ip route
echo

echo "DNS Configuration:"
cat /etc/resolv.conf
echo

echo "Connectivity Tests:"
echo -n "Gateway (***********): "
ping -c 1 -W 2 *********** >/dev/null 2>&1 && echo "OK" || echo "FAIL"

echo -n "DNS (*******): "
ping -c 1 -W 2 ******* >/dev/null 2>&1 && echo "OK" || echo "FAIL"

echo -n "Internet (google.com): "
ping -c 1 -W 2 google.com >/dev/null 2>&1 && echo "OK" || echo "FAIL"

echo
echo "=== End of Network Status ==="
EOF
    
    chmod +x /usr/local/bin/verify-network.sh
    print_success "Network verification script created: /usr/local/bin/verify-network.sh"
}

# Main function
main() {
    print_status "Starting static IP configuration for nuc2..."
    
    check_root
    backup_network_config
    detect_interface
    create_netplan_config
    apply_network_config
    verify_connectivity
    update_hosts_file
    create_verification_script
    display_network_info
    
    print_success "Static IP configuration completed successfully!"
    print_status "You can verify network status anytime with: /usr/local/bin/verify-network.sh"
}

# Run main function
main "$@"
