# Guía Completa de Instalación de Documenso en Proxmox

## Resumen del Proyecto

**Objetivo:** Instalar Documenso (plataforma de firma de documentos open-source) en VM "nuc2" del servidor Proxmox.

**Configuración Target:**
- **Servidor:** prox.stargety.in
- **VM:** nuc2 (ID: 102)
- **IP Estática:** *************
- **Dominio:** sign.stargety.com
- **Sistema:** Ubuntu 24.04.2 LTS Server

---

## Estado Actual

✅ **Completado:**
1. Conexión SSH al servidor Proxmox
2. Análisis del entorno (VMs, red, almacenamiento)
3. Creación de VM nuc2 (ID: 102) con Ubuntu 24.04.2 LTS
4. VM iniciada y ejecutándose
5. Scripts de instalación automatizados creados
6. Documentación completa generada

🔄 **En Progreso:**
- Instalación del sistema operativo Ubuntu Server (requiere acceso manual a consola)

⏳ **Pendiente:**
- Configuración de red estática (*************)
- Instalación de Documenso
- Configuración de dominio (sign.stargety.com)
- Configuración SSL/TLS
- Configuración de seguridad

## Scripts Automatizados Creados

✅ **Scripts Disponibles:**
1. `configure-static-ip.sh` - Configuración de IP estática
2. `install-documenso.sh` - Instalación completa de Documenso
3. `setup-documenso-complete.sh` - Script maestro que ejecuta todo el proceso

---

## Instalación Automatizada (Recomendada)

### Opción 1: Script Maestro (Todo en Uno)

Una vez que Ubuntu Server esté instalado en la VM nuc2, puedes usar el script maestro para automatizar todo el proceso:

```bash
# Transferir scripts a la VM (desde tu máquina local)
scp configure-static-ip.sh install-documenso.sh setup-documenso-complete.sh <EMAIL>:~/

# Conectar a la VM
ssh <EMAIL>

# Ejecutar script maestro
chmod +x setup-documenso-complete.sh
./setup-documenso-complete.sh
```

### Opción 2: Scripts Individuales

Si prefieres ejecutar los pasos por separado:

```bash
# 1. Configurar IP estática
sudo ./configure-static-ip.sh

# 2. Instalar Documenso
./install-documenso.sh
```

---

## Pasos de Instalación Manual

### Paso 1: Acceso a la Consola de la VM

Para completar la instalación de Ubuntu Server, necesitas acceder a la consola de la VM:

1. **Acceso Web a Proxmox:**
   ```
   URL: https://prox.stargety.in:8006
   Usuario: root
   Contraseña: Netflix$1000
   ```

2. **Acceso a la consola de nuc2:**
   - En la interfaz web, navega a: Datacenter > nuc1 > Virtual Machines > 102 (nuc2)
   - Haz clic en "Console" para acceder a la consola de la VM

### Paso 2: Instalación de Ubuntu Server

Durante la instalación de Ubuntu Server, configura:

1. **Configuración de Red:**
   - Selecciona configuración manual de red
   - IP: *************
   - Netmask: ************* (/24)
   - Gateway: ***********
   - DNS: *******, *******

2. **Usuario del Sistema:**
   - Nombre de usuario: `documenso`
   - Contraseña: `Documenso2025!`
   - Nombre del servidor: `nuc2`

3. **Paquetes Adicionales:**
   - Instalar OpenSSH Server
   - Instalar Docker (opcional, recomendado)

### Paso 3: Configuración Post-Instalación

Una vez completada la instalación de Ubuntu, ejecuta estos comandos:

```bash
# Actualizar el sistema
sudo apt update && sudo apt upgrade -y

# Instalar dependencias básicas
sudo apt install -y curl wget git unzip software-properties-common

# Verificar configuración de red
ip addr show
ping -c 4 *******
```

### Paso 4: Instalación de Node.js y dependencias

```bash
# Instalar Node.js 20 LTS
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verificar instalación
node --version
npm --version

# Instalar PostgreSQL
sudo apt install -y postgresql postgresql-contrib

# Configurar PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### Paso 5: Instalación de Documenso

```bash
# Crear directorio para Documenso
sudo mkdir -p /opt/documenso
cd /opt/documenso

# Descargar la última versión
LATEST_VERSION=$(curl -s https://api.github.com/repos/documenso/documenso/releases/latest | grep tag_name | cut -d '"' -f 4)
wget https://github.com/documenso/documenso/archive/refs/tags/${LATEST_VERSION}.zip
unzip ${LATEST_VERSION}.zip
mv documenso-${LATEST_VERSION#v}/* .
rm -rf documenso-${LATEST_VERSION#v} ${LATEST_VERSION}.zip

# Configurar base de datos PostgreSQL
sudo -u postgres createuser documenso
sudo -u postgres createdb documenso_db -O documenso
sudo -u postgres psql -c "ALTER USER documenso PASSWORD 'DocumensoPass2025!';"
```

### Paso 6: Configuración de Documenso

```bash
# Copiar archivo de configuración
cp .env.example .env

# Editar configuración
sudo nano .env
```

**Configuración del archivo .env:**
```env
# Database
DATABASE_URL="postgresql://documenso:DocumensoPass2025!@localhost:5432/documenso_db"

# Application
NEXTAUTH_URL="http://*************:3000"
NEXTAUTH_SECRET="your-super-secret-key-here-change-this"

# Email (configurar según tu proveedor)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USERNAME="<EMAIL>"
SMTP_PASSWORD="your-app-password"
SMTP_FROM="<EMAIL>"

# Application settings
NEXT_PUBLIC_WEBAPP_URL="http://*************:3000"
NEXT_PRIVATE_ENCRYPTION_KEY="your-encryption-key-32-chars-long"
NEXT_PRIVATE_ENCRYPTION_SECONDARY_KEY="your-secondary-key-32-chars-long"
```

### Paso 7: Instalación de dependencias y construcción

```bash
# Instalar dependencias
npm install

# Construir la aplicación
npm run build

# Ejecutar migraciones de base de datos
npm run prisma:migrate:deploy

# Generar cliente Prisma
npm run prisma:generate
```

### Paso 8: Configuración como servicio systemd

```bash
# Crear archivo de servicio
sudo nano /etc/systemd/system/documenso.service
```

**Contenido del archivo de servicio:**
```ini
[Unit]
Description=Documenso Application
After=network.target postgresql.service

[Service]
Type=simple
User=documenso
WorkingDirectory=/opt/documenso
Environment=NODE_ENV=production
ExecStart=/usr/bin/npm start
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# Habilitar y iniciar el servicio
sudo systemctl daemon-reload
sudo systemctl enable documenso
sudo systemctl start documenso

# Verificar estado
sudo systemctl status documenso
```

---

## Configuración de Dominio y SSL

### Paso 9: Configuración de Nginx como Reverse Proxy

```bash
# Instalar Nginx
sudo apt install -y nginx

# Crear configuración del sitio
sudo nano /etc/nginx/sites-available/sign.stargety.com
```

**Configuración de Nginx:**
```nginx
server {
    listen 80;
    server_name sign.stargety.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# Habilitar el sitio
sudo ln -s /etc/nginx/sites-available/sign.stargety.com /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### Paso 10: Configuración SSL con Let's Encrypt

```bash
# Instalar Certbot
sudo apt install -y certbot python3-certbot-nginx

# Obtener certificado SSL
sudo certbot --nginx -d sign.stargety.com

# Verificar renovación automática
sudo certbot renew --dry-run
```

---

## Configuración de DNS

Para que el dominio `sign.stargety.com` apunte a la VM:

1. **Opción 1: DNS Público**
   - Configurar un registro A en tu proveedor DNS
   - Apuntar `sign.stargety.com` a la IP pública del servidor Proxmox
   - Configurar port forwarding en el router (puerto 80 y 443 → *************)

2. **Opción 2: DNS Local/Hosts**
   - Agregar entrada en `/etc/hosts` de las máquinas cliente:
   ```
   ************* sign.stargety.com
   ```

---

## Verificación y Pruebas

### Comandos de Verificación

```bash
# Verificar estado de servicios
sudo systemctl status documenso
sudo systemctl status nginx
sudo systemctl status postgresql

# Verificar conectividad
curl -I http://localhost:3000
curl -I http://*************
curl -I http://sign.stargety.com

# Verificar logs
sudo journalctl -u documenso -f
sudo tail -f /var/log/nginx/access.log
```

### URLs de Acceso

- **Local:** http://*************:3000
- **Con Nginx:** http://*************
- **Con Dominio:** http://sign.stargety.com
- **Con SSL:** https://sign.stargety.com

---

## Credenciales y Configuración

### Credenciales del Sistema
- **VM nuc2 SSH:** documenso@*************
- **Contraseña:** Documenso2025!

### Credenciales de Base de Datos
- **Usuario:** documenso
- **Contraseña:** DocumensoPass2025!
- **Base de datos:** documenso_db

### Configuración de Red
- **IP:** *************/24
- **Gateway:** ***********
- **DNS:** *******, *******

---

## Solución de Problemas

### Problemas Comunes

1. **Documenso no inicia:**
   ```bash
   sudo journalctl -u documenso -n 50
   npm run dev  # Para debugging
   ```

2. **Error de base de datos:**
   ```bash
   sudo -u postgres psql -c "\l"  # Listar bases de datos
   npm run prisma:migrate:reset   # Resetear migraciones
   ```

3. **Problemas de red:**
   ```bash
   sudo netstat -tlnp | grep :3000
   sudo ufw status
   ```

### Logs Importantes
- Documenso: `sudo journalctl -u documenso`
- Nginx: `/var/log/nginx/error.log`
- PostgreSQL: `/var/log/postgresql/`

---

## Próximos Pasos

1. Completar instalación de Ubuntu Server en la consola
2. Configurar red estática
3. Ejecutar scripts de instalación de Documenso
4. Configurar dominio y SSL
5. Realizar pruebas de funcionamiento
6. Documentar credenciales finales
